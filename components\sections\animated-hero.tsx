"use client"

import { useEffect, useMemo, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight, MessageCircle, Users, Award, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";

import Link from "next/link";

const features = [
  {
    icon: Users,
    label: "500+",
    title: "Happy Clients",
    description: "Satisfied customers across Ahmedabad",
  },
  {
    icon: Award,
    label: "50+",
    title: "Premium Projects",
    description: "Luxury developments completed",
  },
  {
    icon: TrendingUp,
    label: "15+",
    title: "Years Experience",
    description: "Trusted expertise in real estate",
  },
]

function AnimatedHero() {
  const [titleNumber, setTitleNumber] = useState(0);
  const titles = useMemo(
    () => ["Property", "Investment", "Dream Home", "Future"],
    []
  );

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (titleNumber === titles.length - 1) {
        // Last word पर रुकने के लिए longer delay
        setTimeout(() => {
          setTitleNumber(0);
        }, 1500); // Extra delay for last word
      } else {
        setTitleNumber(titleNumber + 1);
      }
    }, 2500);
    return () => clearTimeout(timeoutId);
  }, [titleNumber, titles]);

  return (
    <section className="relative min-h-screen bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden flex items-center">
      {/* Background Image */}
      <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>

      {/* Floating Light Shapes */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute w-32 h-32 sm:w-48 sm:h-48 rounded-full bg-white/10 opacity-30 animate-float-minimal top-[20%] left-[5%]"></div>
        <div className="absolute w-24 h-24 sm:w-32 sm:h-32 rounded-full bg-white/15 opacity-40 animate-float-minimal-reverse top-[60%] right-[10%]"></div>
        <div className="absolute w-20 h-20 sm:w-24 sm:h-24 rounded-full bg-white/10 opacity-20 animate-float-minimal top-[40%] right-[15%]"></div>
        <div className="absolute w-28 h-28 sm:w-40 sm:h-40 rounded-full bg-white/10 opacity-35 animate-float-minimal-reverse bottom-[5%] left-[20%]"></div>
      </div>

      {/* Main Hero Content */}
      <div className="container mx-auto px-4 relative z-10 w-full">
        <div className="flex gap-8 pt-24 pb-20 sm:pt-20 sm:pb-20 lg:py-40 items-center justify-center flex-col">
          
          {/* Main Headline with Animated Text */}
          <div className="flex gap-4 flex-col">
            <h1 className="text-4xl md:text-6xl lg:text-7xl max-w-4xl tracking-tighter text-center font-bold">
              <span className="text-white block mb-4">Your Trusted</span>
              <span className="relative flex w-full justify-center overflow-hidden text-center md:pb-4 md:pt-1 h-16 md:h-20 lg:h-24">
                <AnimatePresence mode="wait">
                  <motion.span
                    key={titleNumber}
                    className="absolute font-bold text-white flex items-center justify-center w-full h-full"
                    initial={{ opacity: 0, y: 50, scale: 0.8 }}
                    animate={{
                      opacity: 1,
                      y: 0,
                      scale: 1,
                      transition: {
                        duration: 0.8,
                        ease: [0.25, 0.46, 0.45, 0.94], // Custom easing
                        type: "spring",
                        stiffness: 100,
                        damping: 15
                      }
                    }}
                    exit={{
                      opacity: 0,
                      y: -50,
                      scale: 1.1,
                      transition: {
                        duration: 0.5,
                        ease: "easeInOut"
                      }
                    }}
                    style={{
                      textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                    }}
                  >
                    {titles[titleNumber]}
                  </motion.span>
                </AnimatePresence>
              </span>
              <span className="text-white block mt-4">Partner</span>
            </h1>

            <p className="text-lg md:text-xl leading-relaxed tracking-tight text-white/90 max-w-3xl text-center font-light">
              Experience luxury living redefined with our comprehensive real estate services. From finding your dream
              home to smart investments, we're your trusted partner in Ahmedabad.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-white text-burgundy-600 hover:bg-gray-100 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 group border-0 hover:scale-105 transform text-sm sm:text-base gap-4"
              asChild
            >
              <Link href="/projects">
                <span>Explore Properties</span>
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="border-2 border-white text-white hover:border-white hover:text-burgundy-600 hover:bg-white px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold transition-all duration-300 group hover:scale-105 transform bg-transparent text-sm sm:text-base gap-4"
              asChild
            >
              <Link href="/contact">
                <MessageCircle className="w-4 h-4 group-hover:scale-110 transition-transform" />
                <span>Get Consultation</span>
              </Link>
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="w-full max-w-5xl mx-auto mt-16">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
              {features.map((feature, index) => {
                const IconComponent = feature.icon
                return (
                  <motion.div 
                    key={feature.label} 
                    className="group"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.2 + 1, duration: 0.6 }}
                  >
                    <div className="bg-white rounded-2xl p-6 sm:p-8 shadow-sm border border-gray-100 hover:shadow-xl hover:border-burgundy-200 transition-all duration-500 hover:-translate-y-2 h-44 sm:h-48 flex flex-col justify-center items-center text-center">
                      <div className="w-12 h-12 sm:w-16 sm:h-16 bg-pink-100 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-pink-200 group-hover:scale-110 transition-all duration-300">
                        <IconComponent className="text-pink-600" size={24} />
                      </div>
                      <h3 className="text-3xl sm:text-4xl font-bold text-pink-600 mb-1">{feature.label}</h3>
                      <h4 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">{feature.title}</h4>
                      <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">{feature.description}</p>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default AnimatedHero;
