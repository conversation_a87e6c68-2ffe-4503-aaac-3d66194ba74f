# VaporizeTextCycle Component

A stunning React component that creates a mesmerizing vapor/particle text effect with smooth text cycling animations. Perfect for hero sections, landing pages, and eye-catching text displays.

## Features

- 🌟 **Particle-based text rendering** - Text is converted to particles for smooth vaporization effects
- 🔄 **Automatic text cycling** - Smoothly transitions between multiple text strings
- ⚡ **High performance** - Optimized canvas rendering with proper cleanup
- 📱 **Responsive design** - Adapts to different screen sizes and DPI settings
- 🎨 **Highly customizable** - Control colors, fonts, animation speed, and direction
- 🔧 **TypeScript support** - Fully typed for better development experience
- 👁️ **Viewport optimization** - Only animates when component is visible

## Installation

The component is already integrated into your project. All required dependencies are included:

- `framer-motion` - For smooth animations
- `react` - Core React functionality
- `lucide-react` - For any additional icons (if needed)

## Basic Usage

```tsx
import { VaporizeTextCycle } from "@/components/ui/vapour-text-effect";

function MyComponent() {
  return (
    <VaporizeTextCycle
      texts={["Hello", "World", "Amazing"]}
      font={{
        fontSize: "60px",
        fontWeight: 600,
        fontFamily: "Inter, sans-serif",
      }}
      color="rgb(59, 130, 246)"
      alignment="center"
      className="w-full h-32"
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `texts` | `string[]` | `["Next.js", "React", "TypeScript"]` | Array of text strings to cycle through |
| `font` | `object` | See below | Font configuration object |
| `font.fontSize` | `string` | `"50px"` | Font size (include "px" unit) |
| `font.fontWeight` | `number \| string` | `400` | Font weight |
| `font.fontFamily` | `string` | `"sans-serif"` | Font family |
| `color` | `string` | `"rgb(153, 153, 153)"` | Text color (RGB/RGBA format) |
| `alignment` | `"left" \| "center" \| "right"` | `"left"` | Text alignment |
| `density` | `number` | `1` | Particle density (0.1 - 1.0) |
| `vaporizeSpeed` | `number` | `1` | Speed of vaporization animation |
| `vaporizeSpread` | `number` | `1` | Spread/dispersion of particles |
| `vaporizeDirection` | `"left-to-right" \| "right-to-left"` | `"left-to-right"` | Direction of vaporization |
| `vaporizeDuration` | `number` | `2000` | Duration of vaporization in ms |
| `cycleDuration` | `number` | `4000` | Time between text changes in ms |
| `className` | `string` | `""` | Additional CSS classes |

## Examples

### Hero Section (Current Implementation)
```tsx
<VaporizeTextCycle
  texts={["Property", "Investment", "Dream Home", "Future"]}
  font={{
    fontSize: "clamp(2.5rem, 8vw, 4.5rem)",
    fontWeight: 700,
    fontFamily: "Inter, system-ui, sans-serif",
  }}
  color="rgba(255, 255, 255, 0.95)"
  alignment="center"
  density={0.9}
  vaporizeSpeed={1.3}
  vaporizeSpread={1.8}
  vaporizeDirection="left-to-right"
  vaporizeDuration={2200}
  cycleDuration={4500}
  className="w-full h-full flex items-center justify-center"
/>
```

### Compact Version
```tsx
<VaporizeTextCycle
  texts={["Buy", "Sell", "Lease", "Invest"]}
  font={{
    fontSize: "32px",
    fontWeight: 500,
  }}
  color="rgb(59, 130, 246)"
  alignment="center"
  density={1.0}
  vaporizeSpeed={1.5}
  cycleDuration={3000}
  className="w-full h-16"
/>
```

### Slow and Elegant
```tsx
<VaporizeTextCycle
  texts={["Luxury", "Elegance", "Sophistication"]}
  font={{
    fontSize: "48px",
    fontWeight: 300,
    fontFamily: "serif",
  }}
  color="rgba(0, 0, 0, 0.8)"
  alignment="center"
  density={0.7}
  vaporizeSpeed={0.8}
  vaporizeSpread={1.2}
  vaporizeDuration={3500}
  cycleDuration={5000}
  className="w-full h-24"
/>
```

## Performance Tips

1. **Container Height**: Always provide a fixed height to the container to prevent layout shifts
2. **Viewport Optimization**: The component automatically pauses when not visible
3. **Density Control**: Lower density (0.5-0.8) for better performance on mobile devices
4. **Font Loading**: Use web-safe fonts or ensure custom fonts are loaded before rendering

## Styling

The component uses a canvas element for rendering, so traditional CSS styling won't affect the text appearance. Use the component props instead:

- Use `color` prop for text color (supports RGB/RGBA)
- Use `font` object for typography
- Use `className` for container styling (positioning, dimensions, etc.)

## Browser Support

- ✅ Chrome/Edge (recommended)
- ✅ Firefox
- ✅ Safari
- ✅ Mobile browsers
- ⚠️ IE11 (limited support)

## Integration Notes

This component is currently integrated into:
- Homepage hero section (`components/sections/animated-hero.tsx`)
- Demo page (`app/demo/page.tsx`)

## Troubleshooting

**Text not appearing**: Ensure the container has a defined height and width
**Performance issues**: Reduce `density` prop or increase `cycleDuration`
**Font not loading**: Add a delay or use font loading events
**Particles not animating**: Check that `vaporizeSpeed` and `vaporizeDuration` are reasonable values

## Demo

Visit `/demo` to see various configurations of the component in action.
