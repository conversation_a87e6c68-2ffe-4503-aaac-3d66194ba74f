import type { Metadata } from "next"
import { VaporizeTextCycle } from "@/components/ui/vapour-text-effect"

export const metadata: Metadata = {
  title: "Vapor Text Effect Demo - Dwelling Desire",
  description: "Interactive demonstration of the vapor text effect component",
}

export default function DemoPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
            Vapor Text Effect Demo
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Experience the mesmerizing vapor text animation in action
          </p>
        </div>

        {/* Demo Section 1: Real Estate Theme */}
        <section className="mb-20">
          <h2 className="text-2xl font-semibold text-white mb-8 text-center">
            Real Estate Theme
          </h2>
          <div className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-2xl p-12 text-center">
            <div className="h-32 flex items-center justify-center">
              <VaporizeTextCycle
                texts={["Dwelling", "Desire", "Dreams", "Design"]}
                font={{
                  fontSize: "60px",
                  fontWeight: 600,
                  fontFamily: "Inter, sans-serif",
                }}
                color="rgba(255, 255, 255, 0.95)"
                alignment="center"
                density={0.8}
                vaporizeSpeed={1.2}
                vaporizeSpread={1.5}
                vaporizeDirection="left-to-right"
                vaporizeDuration={2500}
                cycleDuration={4000}
                className="w-full h-full"
              />
            </div>
          </div>
        </section>

        {/* Demo Section 2: Property Types */}
        <section className="mb-20">
          <h2 className="text-2xl font-semibold text-white mb-8 text-center">
            Property Types
          </h2>
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-12 text-center">
            <div className="h-24 flex items-center justify-center">
              <VaporizeTextCycle
                texts={["Luxury Villas", "Modern Apartments", "Commercial Spaces", "Investment Properties"]}
                font={{
                  fontSize: "48px",
                  fontWeight: 500,
                  fontFamily: "Inter, sans-serif",
                }}
                color="rgb(59, 130, 246)"
                alignment="center"
                density={0.9}
                vaporizeSpeed={1.0}
                vaporizeSpread={1.2}
                vaporizeDirection="right-to-left"
                vaporizeDuration={2000}
                cycleDuration={3500}
                className="w-full h-full"
              />
            </div>
          </div>
        </section>

        {/* Demo Section 3: Services */}
        <section className="mb-20">
          <h2 className="text-2xl font-semibold text-white mb-8 text-center">
            Our Services
          </h2>
          <div className="bg-gradient-to-r from-emerald-600 to-teal-600 rounded-2xl p-12 text-center">
            <div className="h-20 flex items-center justify-center">
              <VaporizeTextCycle
                texts={["Buy", "Sell", "Lease", "Invest"]}
                font={{
                  fontSize: "40px",
                  fontWeight: 700,
                  fontFamily: "Inter, sans-serif",
                }}
                color="rgba(255, 255, 255, 0.9)"
                alignment="center"
                density={1.0}
                vaporizeSpeed={1.5}
                vaporizeSpread={2.0}
                vaporizeDirection="left-to-right"
                vaporizeDuration={1800}
                cycleDuration={3000}
                className="w-full h-full"
              />
            </div>
          </div>
        </section>

        {/* Demo Section 4: Locations */}
        <section>
          <h2 className="text-2xl font-semibold text-white mb-8 text-center">
            Prime Locations
          </h2>
          <div className="bg-gradient-to-r from-orange-600 to-red-600 rounded-2xl p-12 text-center">
            <div className="h-28 flex items-center justify-center">
              <VaporizeTextCycle
                texts={["Prahlad Nagar", "Satellite", "Bopal", "Shela"]}
                font={{
                  fontSize: "52px",
                  fontWeight: 600,
                  fontFamily: "Inter, sans-serif",
                }}
                color="rgba(255, 255, 255, 0.95)"
                alignment="center"
                density={0.7}
                vaporizeSpeed={0.8}
                vaporizeSpread={1.3}
                vaporizeDirection="right-to-left"
                vaporizeDuration={3000}
                cycleDuration={4500}
                className="w-full h-full"
              />
            </div>
          </div>
        </section>

        <div className="text-center mt-16">
          <p className="text-gray-400">
            This component is now integrated into the homepage hero section
          </p>
        </div>
      </div>
    </main>
  )
}
